<?php

namespace App\Modules\RequestDelete\Services;

use App\Events\DomainHistoryEvent;
use App\Mail\UserDeleteRequestMail;
use App\Modules\Client\Constants\DomainStatus;
use App\Modules\Epp\Services\EppDomainService;
use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use App\Modules\RequestDelete\Services\DomainEppCancellationJobService;
use App\Util\Constant\UserDomainStatus;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class DomainDeleteService
{
    private Carbon $now;
    private bool $isJob;

    public function __construct()
    {
        $this->now = Carbon::now();
        $this->isJob = config('app.use_jobs', true);
    }

    public static function instance()
    {
        return new self;
    }

    public function approveDeleteRequestSave($request)
    {
        $data = $request->all();

        if ($this->isJob) {
            $this->jobDispatch($data, 'approve');
        } else {
            $this->whenJobIsDisabled($data, 'approve');
        }
    }

    public function rejectDeleteRequestSave($request)
    {
        self::updateDomainDeletionRequestTable($request, 0);

        DB::client()->table('domains')->where('id', $request['domainId'])->update([
            'status' => DomainStatus::ACTIVE,
            'deleted_at' => null,
            'updated_at' => now(),
        ]);
    }

    public function createDeleteRequestSave($request)
    {
        $data = $request->all();

        if ($this->isJob) {
            $this->jobDispatch($data, 'create');
        } else {
            $this->whenJobIsDisabled($data, 'create');
        }
    }

    private function jobDispatch(array $data, string $action): void
    {
        $adminId = Auth::id();
        $adminName = Auth::user()->name ?? 'System';
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $supportNote = "Request delete approved by {$adminName}";

        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userID'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $supportNote,
            $adminId,
            $adminName,
            $adminEmail
        );

        if ($action === 'approve') {
            self::userNotification($data);
            self::userEmailNotification($data);
            self::domainHistory($data);
        } elseif ($action === 'create') {
            self::userNotification($data);
            self::userEmailNotification($data);
            self::domainHistory($data);
        }
    }

    private function whenJobIsDisabled(array $data, string $action): void
    {
        EppDomainService::instance()->callEppDomainDelete($data['domainName']);
        EppDomainService::instance()->callDatastoreDomainDelete($data['domainName']);

        if ($action === 'approve') {
            self::localDelete($data);
            self::userNotification($data);
            self::userEmailNotification($data);
            self::domainHistory($data);

            DB::client()->table('pending_domain_deletions')->insert([
                'registered_domain_id' => $data['domainId'],
                'deleted_by' => Auth::user()->email,
                'deleted_at' => now(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        } elseif ($action === 'create') {
            self::newDomainDelete($data);
            self::domainHistory($data);
            self::localDelete($data, skipUpdate: true);
            self::userNotification($data);
            self::userEmailNotification($data);
        }
    }

    private function localDelete($requestData, bool $skipUpdate = false)
    {
        if (!$skipUpdate) {
            self::updateDomainDeletionRequestTable($requestData);
        }

        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $requestData['domainId'])
            ->update($updates);
    }

    private function updateDomainDeletionRequestTable($requestData, $authID = null)
    {
        $agentID = $authID ?? Auth::id();

        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->exists();

        if (!$exists) {
            return;
        }

        $date = Carbon::parse($requestData['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->update([
                'support_agent_id'   => $agentID,
                'support_agent_name'=> Auth::user()->name . ' (' . Auth::user()->email . ')',
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => 'Request delete approved by ' . Auth::user()->name,
                'is_refunded'        => $is_refunded,
            ]);
    }

    private function userNotification($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Approved',
            'message'      => 'Your request to delete the domain "' . $domainName . '" has been approved.',
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotification($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $message = [
            'subject'  => 'Domain Deletion Request Approved',
            'greeting' => 'Greetings!',
            'body'     => 'Your request to delete the domain "' . $domainName . '" has been approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.',
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));

        self::emailTrack($userEmail, $message, $requestData['domainId']);
    }

    private function newDomainDelete($data)
    {
        $date = Carbon::parse($data['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')->insert([
            'user_id'             => $data['userID'],
            'domain_id'           => $data['domainId'],
            'reason'              => $data['reason'],
            'support_agent_id'    => Auth::id(),
            'support_agent_name'  => Auth::user()->name . ' (' . Auth::user()->email . ')',
            'deleted_at'          => now(),
            'feedback_date'       => now(),
            'support_note'        => 'Request delete approved by ' . Auth::user()->name,
            'is_refunded'         => $is_refunded,
        ]);
    }

    private function getUserByDomain($domainId) 
    {
        return DB::client()->table('registered_domains')
            ->select('users.id as user_id', 'users.email', 'users.first_name', 'users.last_name')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.id', $domainId)
            ->first();
    }

    private function emailTrack($email, array $payload, $domainId = null) 
    {
        $userId = null;
        $userName = null;

        if ($domainId) {
            $user = $this->getUserByDomain($domainId);
            if ($user) {
                $userId = $user->user_id;
                $userName = $user->first_name . ' ' . $user->last_name;
            }
        }

        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => $userId,
                'name' => $userName ?? 'System',
                'recipient_email' => $email,
                'subject' => 'Domain Deletion Request Approved',
                'email_type' => 'Domain Deletion Request Approved',
                'email_body' => json_encode($payload),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

        return $emailSent;
    }

    private function domainHistory($data)
    {
        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $data['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $data['userID'],
            'status'    => 'success',
            'message'   => 'Domain "' . $data['domainName'] . '" deleted by ' . Auth::user()->name . ' (' . Auth::user()->email . ')',
            'payload'   => json_encode($data),
        ]);
    }

    public function processExpiredRequests()
    {
        // Find domains with IN_PROCESS status that have been requested more than 24 hours ago
        $expiredRequests = DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->select([
                'domain_cancellation_requests.domain_id as domainId',
                'domains.name as domainName',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.requested_at as createdDate'
            ])
            ->where('domains.status', 'IN_PROCESS')
            ->whereNull('domain_cancellation_requests.support_agent_id')
            ->where('domain_cancellation_requests.requested_at', '<=', now()->subHours(24))
            ->get();

        foreach ($expiredRequests as $request) {
            $data = [
                'domainId' => $request->domainId,
                'domainName' => $request->domainName,
                'userID' => $request->userID,
                'userEmail' => $request->userEmail,
                'reason' => $request->reason,
                'createdDate' => $request->createdDate,
            ];

            // Auto-approve the request
            if ($this->isJob) {
                $this->jobDispatchForScheduler($data);
            } else {
                $this->whenJobIsDisabledForScheduler($data);
            }
        }

        return count($expiredRequests);
    }

    private function jobDispatchForScheduler(array $data): void
    {
        $adminId = 1; // System admin ID
        $adminName = 'System';
        $adminEmail = '<EMAIL>';
        $supportNote = "Request delete automatically approved by system (24+ hours)";

        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userID'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $supportNote,
            $adminId,
            $adminName,
            $adminEmail
        );

        self::userNotificationForScheduler($data);
        self::userEmailNotificationForScheduler($data);
        self::domainHistoryForScheduler($data);
    }

    private function whenJobIsDisabledForScheduler(array $data): void
    {
        EppDomainService::instance()->callEppDomainDelete($data['domainName']);
        EppDomainService::instance()->callDatastoreDomainDelete($data['domainName']);

        self::localDeleteForScheduler($data);
        self::userNotificationForScheduler($data);
        self::userEmailNotificationForScheduler($data);
        self::domainHistoryForScheduler($data);

        DB::client()->table('pending_domain_deletions')->insert([
            'registered_domain_id' => $data['domainId'],
            'deleted_by' => '<EMAIL>',
            'deleted_at' => now(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    private function localDeleteForScheduler($requestData)
    {
        self::updateDomainDeletionRequestTableForScheduler($requestData);

        $timestamp = now();

        $updates = [
            'status'     => UserDomainStatus::DELETED,
            'deleted_at' => $timestamp,
            'updated_at' => $timestamp,
        ];

        DB::client()->table('registered_domains')
            ->where('domain_id', $requestData['domainId'])
            ->update($updates);

        DB::client()->table('domains')
            ->where('id', $requestData['domainId'])
            ->update($updates);
    }

    private function updateDomainDeletionRequestTableForScheduler($requestData)
    {
        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->exists();

        if (!$exists) {
            return;
        }

        $date = Carbon::parse($requestData['createdDate']);
        $is_refunded = $date->greaterThanOrEqualTo(now()->subDays(5)) ? false : true;

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $requestData['domainId'])
            ->update([
                'support_agent_id'   => 1,
                'support_agent_name' => 'System (<EMAIL>)',
                'deleted_at'         => now(),
                'feedback_date'      => now(),
                'support_note'       => 'Request delete automatically approved by system (24+ hours)',
                'is_refunded'        => $is_refunded,
            ]);
    }

    private function userNotificationForScheduler($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Approved',
            'message'      => 'Your request to delete the domain "' . $domainName . '" has been automatically approved.',
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotificationForScheduler($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $message = [
            'subject'  => 'Domain Deletion Request Approved',
            'greeting' => 'Greetings!',
            'body'     => 'Your request to delete the domain "' . $domainName . '" has been automatically approved. The domain will be removed from your account and queued for deletion shortly. This action is final and cannot be undone.',
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));

        self::emailTrack($userEmail, $message, $requestData['domainId']);
    }

    private function domainHistoryForScheduler($data)
    {
        DB::client()->table('domain_transaction_histories')->insert([
            'domain_id' => $data['domainId'],
            'type'      => 'DOMAIN_DELETED',
            'user_id'   => $data['userID'],
            'status'    => 'success',
            'message'   => 'Domain "' . $data['domainName'] . '" automatically deleted by system (24+ hours)',
            'payload'   => json_encode($data),
        ]);
    }
}
