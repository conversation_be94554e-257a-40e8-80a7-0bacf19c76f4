import { Dialog, Transition } from "@headlessui/react";
import { Fragment, useState } from "react";
import { router } from "@inertiajs/react";
import { toast } from "react-toastify";
import setDefaultDateFormat from "@/Util/setDefaultDateFormat";

export default function ConfirmDomainDeletionModal({ isOpen, onClose, deletionRequest }) {
    const [submitting, setSubmitting] = useState(false);
    const [inputValue, setInputValue] = useState("");
    const [isValid, setIsValid] = useState(false);
    const [showConfirmStep, setShowConfirmStep] = useState(false);

    const [errors, setErrors] = useState({});

    const handleInputChange = (e) => {
        const value = e.target.value;
        setInputValue(value);
        setIsValid(value.trim().toLowerCase() === deletionRequest.email.toLowerCase());
    };

    const handleSubmit = (e) => {
        e.preventDefault();

        // Clear previous error
        setErrors({});

        setShowConfirmStep(true);
    };

    const handleConfirm = () => {
        if (!isValid) return;

        setSubmitting(true);
        setErrors({});

        axios.post(
            route("domain.delete-request.approve"),
            {
                domainName: deletionRequest.domainName,
                userEmail: deletionRequest.email,
                domainId: deletionRequest.domain_id,
                createdDate: deletionRequest.created_at,
                userID: deletionRequest.user_id,
                email: inputValue,
            },
        )
        .then((response) => {
                toast.success("Domain deletion finalized successfully.");
                onClose();
                setSubmitting(false);
                router.visit(route("domain.delete-request.view"));
                return response;
            })
            .catch((error) => {
                if (error.response?.status === 422) {
                    setErrors(error.response.data.errors);
                    setShowConfirmStep(false); // go back to input if validation fails
                    setSubmitting(false);
                } else {
                    console.log(error.response);
                }
                return error.response;
            });
        // router.post(
        //     route("domain.delete-request.approve"),
        //      {
        //         domainDeletion: deletionRequest,
        //         support_note: note,
        //         email: inputValue,
        //     },
        //     {
        //         onSuccess: () => {
        //             toast.success("Domain deletion finalized successfully.");
        //         },
        //         onError: (errors) => {
        //             toast.error(
        //                 "Failed to delete the domain. Please try again."
        //             );
        //             console.error(errors);
        //         },
        //     }
        // );
    };

    const resetState = () => {
        setInputValue("");
        setIsValid(false);
        setShowConfirmStep(false);
        setSubmitting(false);
        setErrors({});
        onClose();
    };

    return (
        <Transition appear show={isOpen} as={Fragment}>
            <Dialog as="div" className="relative z-50" onClose={resetState}>
                <Transition.Child
                    as={Fragment}
                    enter="ease-out duration-300"
                    enterFrom="opacity-0"
                    enterTo="opacity-100"
                    leave="ease-in duration-200"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                >
                    <div className="fixed inset-0 bg-black bg-opacity-30" />
                </Transition.Child>

                <div className="fixed inset-0 overflow-y-auto">
                    <div className="flex min-h-full items-center justify-center p-4 text-center">
                        <Transition.Child
                            as={Fragment}
                            enter="ease-out duration-300"
                            enterFrom="opacity-0 scale-95"
                            enterTo="opacity-100 scale-100"
                            leave="ease-in duration-200"
                            leaveFrom="opacity-100 scale-100"
                            leaveTo="opacity-0 scale-95"
                        >
                            <Dialog.Panel className="w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                                <Dialog.Title
                                    as="h3"
                                    className="text-xl font-bold leading-6 text-gray-900"
                                >
                                    {deletionRequest.domainName}
                                </Dialog.Title>

                                {deletionRequest.support_note && (
                                    <div className="border border-blue-300 bg-blue-50 rounded-md p-4 mt-4">
                                        <p className="text-blue-700 font-semibold mb-2">Support Feedback:</p>
                                        <p><strong>Support Agent:</strong> {deletionRequest.support_agent_name}</p>
                                        <p><strong>Feedback Date:</strong> {deletionRequest.feedback_date}</p>
                                        <p><strong>Note:</strong> {deletionRequest.support_note}</p>
                                    </div>
                                )}

                                {!deletionRequest.support_note?.trim() && (
                                    <>
                                        {!showConfirmStep ? (
                                            <>
                                                <div className="border border-red-300 bg-red-50 rounded-md p-4 mb-4 mt-4">
                                                    <p className="text-red-700 font-semibold mb-2">Request Details:</p>
                                                    <p><strong>Client Name:</strong> {deletionRequest.first_name} {deletionRequest.last_name}</p>
                                                    <p><strong>Client Email:</strong> {deletionRequest.email}</p>
                                                    <p><strong>Client Domain:</strong> {deletionRequest.domainName}</p>
                                                    <p><strong>Request Date:</strong> {setDefaultDateFormat(deletionRequest.requested_at)} {new Date(deletionRequest.requested_at + "Z").toLocaleTimeString()}</p>
                                                    <p className="break-all w-full"><strong>Reason for Deletion:</strong> {deletionRequest.reason}</p>
                                                </div>

                                                <div className="mt-4 mb-6 border border-blue-200 bg-blue-50 rounded p-4">
                                                    <p className="text-blue-700 font-semibold mb-2">Support Feedback</p>
                                                    <p className="text-gray-700 mb-4">
                                                        This request will be approved with the following support note:
                                                        <strong> "Request delete approved by admin"</strong>
                                                    </p>

                                                    <div className="flex justify-end gap-3 mt-4">
                                                        <button
                                                            type="button"
                                                            className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                                                            onClick={resetState}
                                                        >
                                                            Cancel
                                                        </button>
                                                        <button
                                                            type="button"
                                                            onClick={handleSubmit}
                                                            className="bg-blue-600 px-4 py-2 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                                                            disabled={submitting}
                                                        >
                                                            {submitting ? "Submitting..." : "Approve"}
                                                        </button>
                                                    </div>
                                                </div>
                                            </>
                                        ) : (
                                            <div className="mt-4 mb-6 border border-yellow-200 bg-yellow-50 rounded p-4">
                                                <p className="text-yellow-800 font-semibold mb-2">
                                                    Please type <strong>{deletionRequest.email}</strong> to confirm deletion.
                                                </p>
                                                <input
                                                    type="text"
                                                    value={inputValue}
                                                    onChange={handleInputChange}
                                                    placeholder="Enter email address"
                                                    className="w-full border rounded px-4 py-2 mt-2 focus:ring-2 focus:ring-yellow-400"
                                                />
                                                <div className="flex justify-end gap-3 mt-4">
                                                    <button
                                                        type="button"
                                                        className="px-4 py-2 bg-gray-200 text-gray-800 rounded hover:bg-gray-300"
                                                        onClick={() => setShowConfirmStep(false)}
                                                    >
                                                        Back
                                                    </button>
                                                    <button
                                                        type="button"
                                                        onClick={handleConfirm}
                                                        disabled={!isValid}
                                                        className={`px-4 py-2 rounded text-white ${
                                                            isValid
                                                                ? "bg-green-600 hover:bg-green-700"
                                                                : "bg-green-300 cursor-not-allowed"
                                                        }`}
                                                    >
                                                        {submitting ? "Submitting..." : "Confirm"}
                                                    </button>
                                                </div>
                                            </div>
                                        )}
                                    </>
                                )}
                            </Dialog.Panel>
                        </Transition.Child>
                    </div>
                </div>
            </Dialog>
        </Transition>
    );
}
