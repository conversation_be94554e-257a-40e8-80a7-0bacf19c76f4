<?php

namespace App\Modules\DomainHistory\Services;

use App\Traits\CursorPaginate;
use Illuminate\Support\Facades\DB;
use App\Modules\DomainHistory\Constants\TransactionType;
use Illuminate\Http\Request;
use App\Modules\CustomLogger\Services\AuthLogger;

class DomainHistoryService
{
    use CursorPaginate;
    private static $pageLimit = 20;

    public function getDomainHistories($filters)
    {
        $sortOrder = $filters['sortOrder'] ?? 'desc';
        $orderby = $filters['orderby'] ?? 'sort_id';

        self::$pageLimit = $filters['limit'] ?? 20;

        $query = DB::table('public.domain_transaction_histories AS dth1')
            ->join('public.domains', 'dth1.domain_id', '=', 'domains.id')
            ->leftJoin('public.users', 'dth1.user_id', '=', 'users.id')
            ->select(
                'domains.id',
                'domains.name',
                'domains.client_status',
                'domains.status',
                'domains.created_at',
                'users.id as userId',
                DB::raw('(SELECT type FROM public.domain_transaction_histories WHERE domain_id = domains.id ORDER BY id DESC LIMIT 1) as type'),
                DB::raw('MAX(dth1.created_at) as created_at'),
                DB::raw('MAX(dth1.id) as sort_id'),
                DB::raw("COALESCE(MAX(users.email), 'Server') as domain_email")
            )
            ->groupBy('domains.id', 'domains.name', 'domains.client_status', 'domains.status', 'domains.created_at', 'users.id');

        if ($orderby === 'name:asc') {
            $query->orderBy('domains.name', 'asc');
        } elseif ($orderby === 'name:desc') {
            $query->orderBy('domains.name', 'desc');
        } else {
            $query->orderBy('sort_id', $sortOrder);
        }

        $this->applyFilters($query, $filters);

        $filterParams = array_filter($filters, function($value) {
            return !is_null($value) && $value !== '';
        });

        return $query->paginate(self::$pageLimit)->appends($filterParams);
    }

    public function getTransactionTypes()
    {
        return [
            'DOMAIN_UNLOCKED' => TransactionType::DOMAIN_UNLOCKED,
            'DOMAIN_LOCKED' => TransactionType::DOMAIN_LOCKED,
            'DOMAIN_CREATED' => TransactionType::DOMAIN_CREATED,
            'DOMAIN_DELETED' => TransactionType::DOMAIN_DELETED,
            'DOMAIN_RENEWAL' => TransactionType::DOMAIN_RENEWAL,
            'DOMAIN_UPDATED' => TransactionType::DOMAIN_UPDATED,
            'DOMAIN_CATEGORY_UPDATED' => TransactionType::DOMAIN_CATEGORY_UPDATED,
            'DOMAIN_CONTACT_UPDATED' => TransactionType::DOMAIN_CONTACT_UPDATED,
            'PUSH_REQUEST_APPROVAL' => TransactionType::PUSH_REQUEST_APPROVAL,
            'PUSH_REQUEST_APPROVED' => TransactionType::PUSH_REQUEST_APPROVED,
            'PUSH_REQUEST_CANCELLED' => TransactionType::PUSH_REQUEST_CANCELLED,
            'PUSH_REQUEST_DECLINED' => TransactionType::PUSH_REQUEST_DECLINED,
            'TRANSFER_INBOUND_REQUEST' => TransactionType::TRANSFER_INBOUND_REQUEST,
            'TRANSFER_INBOUND_APPROVED' => TransactionType::TRANSFER_INBOUND_APPROVED,
            'TRANSFER_INBOUND_REJECTED' => TransactionType::TRANSFER_INBOUND_REJECTED,
            'TRANSFER_OUTBOUND_REQUEST' => TransactionType::TRANSFER_OUTBOUND_REQUEST,
            'TRANSFER_OUTBOUND_APPROVED' => TransactionType::TRANSFER_OUTBOUND_APPROVED,
            'TRANSFER_OUTBOUND_REJECTED' => TransactionType::TRANSFER_OUTBOUND_REJECTED,
            'TRANSFER_REQUEST_CANCELLED' => TransactionType::TRANSFER_REQUEST_CANCELLED,
            'TRANSFER_PURCHASE_COMPLETED' => TransactionType::TRANSFER_PURCHASE_COMPLETED,
            'TRANSFER_PURCHASE_CANCELLED' => TransactionType::TRANSFER_PURCHASE_CANCELLED,
            'TRANSFER_PURCHASE_PENDING' => TransactionType::TRANSFER_PURCHASE_PENDING,
            'NAMESERVER_UPDATED' => TransactionType::NAMESERVER_UPDATED,
            'DOMAIN_REDEMPTION' => TransactionType::DOMAIN_REDEMPTION,
            'DOMAIN_CANCELLATION_REQUEST' => TransactionType::DOMAIN_CANCELLATION_REQUEST,
        ];
    }

    public function prepareDomainHistoryData(Request $request)
    {
        $filters = $request->only(['sortOrder', 'orderby', 'type', 'email', 'date', 'search', 'limit', 'status']);

        $domains = $this->getDomainHistories($filters);
        $transactionTypes = $this->getTransactionTypes();

        return [
            'domains' => $domains,
            'transactionTypes' => $transactionTypes,
            'pagination' => $this->getPaginationData($domains),
            'search' => $filters['search'] ?? '',
        ];
    }

    // Private functions

   

    private function applyFilters($query, $filters)
    {
        
        if (!empty($filters['type'])) {
            $query->having(
                DB::raw('(SELECT type FROM public.domain_transaction_histories WHERE domain_id = domains.id ORDER BY id DESC LIMIT 1)'),
                '=',
                ($filters['type'])
            );
        }

        if (!empty($filters['email'])) {
            if ($filters['email'] === 'server') {
                $query->having(DB::raw('COALESCE(MAX(users.email), \'Server\')'), '=', 'Server');
            } else {
                $query->having(DB::raw('COALESCE(MAX(users.email), \'Server\')'), 'LIKE', $filters['email'] . '%');
            }
        }

        if(!empty($filters['status'])) {
            $this->applyStatusFilter($query, $filters['status']);
        }

        if (!empty($filters['date'])) {
            $this->applyDateFilter($query, $filters['date']);
        }

        if (!empty($filters['search'])) {
            $this->applySearch($query, $filters['search']);
        }

    }

    private function applySearch($query, $search)
    {
        if ($search) {
            $query->where('domains.name', 'LIKE', $search . '%');
        }
    }

    private function applyStatusFilter($query, $status) 
    {
        $isHold = $status === 'hold';
        $query->where(function($query) use ($isHold) {
            $query->whereRaw("(
                CASE 
                    WHEN domains.client_status IS NULL THEN " . ($isHold ? 'false' : 'true') . "
                    WHEN domains.client_status = '[]' THEN " . ($isHold ? 'false' : 'true') . "
                    WHEN domains.client_status::text LIKE '%\"clientHold\"%' THEN " . ($isHold ? 'true' : 'false') . "
                    ELSE " . ($isHold ? 'false' : 'true') . "
                END
            )::boolean");
        });
    }

    private function applyDateFilter($query, $dateRange)
    {
        $today = now();
        switch ($dateRange) {
            case 'today':
                $query->having(DB::raw('MAX(dth1.created_at)::date'), '=', $today->toDateString());
                break;
            case 'yesterday':
                $query->having(DB::raw('MAX(dth1.created_at)::date'), '=', $today->subDay()->toDateString());
                break;
            case 'last 7 days':
                $query->having(DB::raw('MAX(dth1.created_at)'), '<=', $today->subDays(7));
                break;
            case 'last 30 days':
                $query->having(DB::raw('MAX(dth1.created_at)'), '<=', $today->subDays(30));
                break;
        }
    }

    private function getPaginationData($domains)
    {
        return [
            'onFirstPage' => $domains->onFirstPage(),
            'onLastPage' => $domains->onLastPage(),
            'nextPageUrl' => $domains->nextPageUrl(),
            'previousPageUrl' => $domains->previousPageUrl(),
            'itemCount' => $domains->count(),
            'total' => $domains->total(),
        ];
    }
}
